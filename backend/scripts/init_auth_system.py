#!/usr/bin/env python3
# scripts/init_auth_system.py - 认证系统初始化脚本
"""
认证系统初始化脚本

功能：
1. 初始化数据库表结构
2. 创建默认角色和权限
3. 创建超级管理员账户
4. 配置Authing集成
5. 验证系统配置
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.database import db_manager, get_db_session
from app.models.auth_models import AuthingUser, Permission, Role
from app.modules.user_management.services.permission_service import PermissionService
from app.modules.user_management.services.role_service import RoleService
from app.modules.user_management.services.user_service import UserService
from sqlalchemy.orm import Session


class AuthSystemInitializer:
    """认证系统初始化器"""

    def __init__(self):
        self.role_service = RoleService()
        self.permission_service = PermissionService()
        self.user_service = UserService()

    def init_database_tables(self):
        """初始化数据库表"""
        print("🗄️  初始化数据库表...")
        try:
            db_manager.create_all_tables()
            print("✅ 数据库表创建成功")
            return True
        except Exception as e:
            print(f"❌ 数据库表创建失败: {e}")
            return False

    def init_default_permissions(self, db: Session):
        """初始化默认权限"""
        print("🔑 创建默认权限...")
        try:
            success = self.permission_service.create_default_permissions(db)
            if success:
                print("✅ 默认权限创建成功")
                return True
            else:
                print("⚠️  默认权限可能已存在")
                return True
        except Exception as e:
            print(f"❌ 默认权限创建失败: {e}")
            return False

    def init_default_roles(self, db: Session):
        """初始化默认角色"""
        print("👥 创建默认角色...")
        try:
            success = self.role_service.create_default_roles(db)
            if success:
                print("✅ 默认角色创建成功")
                return True
            else:
                print("⚠️  默认角色可能已存在")
                return True
        except Exception as e:
            print(f"❌ 默认角色创建失败: {e}")
            return False

    def assign_default_permissions(self, db: Session):
        """分配默认权限给角色"""
        print("🔗 分配权限给角色...")
        try:
            success = self.permission_service.assign_default_permissions_to_roles(db)
            if success:
                print("✅ 权限分配成功")
                return True
            else:
                print("⚠️  权限分配可能已存在")
                return True
        except Exception as e:
            print(f"❌ 权限分配失败: {e}")
            return False

    def create_super_admin(self, db: Session, email: str, name: str):
        """创建超级管理员账户"""
        print(f"👑 创建超级管理员账户: {email}")
        try:
            # 检查用户是否已存在
            existing_user = self.user_service.get_user_by_email(db, email)
            if existing_user:
                print("⚠️  超级管理员账户已存在")
                return existing_user

            # 创建超级管理员用户
            admin_data = {
                "email": email,
                "name": name,
                "authing_user_id": f"admin_{email}",
                "is_active": True,
                "is_email_verified": True,
                "is_phone_verified": False,
            }

            admin_user = self.user_service.create_user(db, admin_data)

            # 分配超级管理员角色
            super_admin_role = self.role_service.get_role_by_code(db, "super_admin")
            if super_admin_role:
                self.role_service.assign_role_to_user(
                    db, admin_user.id, super_admin_role.id
                )
                print("✅ 超级管理员账户创建成功")
                return admin_user
            else:
                print("❌ 找不到超级管理员角色")
                return None

        except Exception as e:
            print(f"❌ 超级管理员账户创建失败: {e}")
            return None

    def verify_system_config(self, db: Session):
        """验证系统配置"""
        print("🔍 验证系统配置...")

        errors = []

        # 检查环境变量
        required_env_vars = [
            "JWT_SECRET_KEY",
            "DB_HOST",
            "DB_NAME",
            "DB_USER",
            "DB_PASSWORD",
        ]

        for var in required_env_vars:
            if not os.getenv(var):
                errors.append(f"环境变量 {var} 未设置")

        # 检查数据库连接
        try:
            db_manager.test_connection()
            print("✅ 数据库连接正常")
        except Exception as e:
            errors.append(f"数据库连接失败: {e}")

        # 检查默认角色
        default_roles = ["super_admin", "admin", "agent", "user"]
        for role_code in default_roles:
            role = self.role_service.get_role_by_code(db, role_code)
            if not role:
                errors.append(f"默认角色 {role_code} 不存在")

        # 检查默认权限
        default_permissions = ["user:read", "profile:read", "system:config"]
        for perm_code in default_permissions:
            permission = self.permission_service.get_permission_by_code(db, perm_code)
            if not permission:
                errors.append(f"默认权限 {perm_code} 不存在")

        if errors:
            print("❌ 系统配置验证失败:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ 系统配置验证通过")
            return True

    def print_summary(self, admin_user: AuthingUser = None):
        """打印初始化摘要"""
        print("\n" + "=" * 60)
        print("🎉 柴管家认证系统初始化完成！")
        print("=" * 60)

        if admin_user:
            print(f"\n👑 超级管理员账户信息:")
            print(f"   邮箱: {admin_user.email}")
            print(f"   姓名: {admin_user.name}")
            print(f"   用户ID: {admin_user.id}")
            print(f"   创建时间: {admin_user.created_at}")

        print(f"\n📊 系统组件状态:")
        print(f"   ✅ 数据库表结构")
        print(f"   ✅ 默认角色和权限")
        print(f"   ✅ RBAC权限系统")
        print(f"   ✅ JWT认证机制")
        print(f"   ✅ API权限验证")

        print(f"\n🔗 API接口:")
        print(f"   认证接口: /api/v1/auth/*")
        print(f"   用户管理: /api/v1/users/*")
        print(f"   角色管理: /api/v1/roles/*")
        print(f"   权限管理: /api/v1/permissions/*")

        print(f"\n📝 后续步骤:")
        print(f"   1. 配置Authing应用信息")
        print(f"   2. 设置JWT密钥和过期时间")
        print(f"   3. 配置CORS和安全策略")
        print(f"   4. 部署到生产环境")

        print(f"\n🛡️  安全提醒:")
        print(f"   ⚠️  请修改默认JWT密钥")
        print(f"   ⚠️  请在生产环境中使用HTTPS")
        print(f"   ⚠️  请定期更新依赖包")
        print(f"   ⚠️  请备份数据库")

        print("\n" + "=" * 60)


def main():
    """主函数"""
    print("🚀 开始初始化柴管家认证系统...")
    print("=" * 60)

    initializer = AuthSystemInitializer()

    # 1. 初始化数据库表
    if not initializer.init_database_tables():
        print("❌ 数据库初始化失败，退出程序")
        sys.exit(1)

    # 2. 初始化权限和角色
    with get_db_session() as db:
        # 创建默认权限
        if not initializer.init_default_permissions(db):
            print("❌ 权限初始化失败，退出程序")
            sys.exit(1)

        # 创建默认角色
        if not initializer.init_default_roles(db):
            print("❌ 角色初始化失败，退出程序")
            sys.exit(1)

        # 分配权限给角色
        if not initializer.assign_default_permissions(db):
            print("❌ 权限分配失败，退出程序")
            sys.exit(1)

        # 3. 创建超级管理员账户
        admin_email = input(
            "\n请输入超级管理员邮箱 (默认: <EMAIL>): "
        ).strip()
        if not admin_email:
            admin_email = "<EMAIL>"

        admin_name = input("请输入超级管理员姓名 (默认: 系统管理员): ").strip()
        if not admin_name:
            admin_name = "系统管理员"

        admin_user = initializer.create_super_admin(db, admin_email, admin_name)

        # 4. 验证系统配置
        config_valid = initializer.verify_system_config(db)

        # 5. 打印初始化摘要
        initializer.print_summary(admin_user)

        if not config_valid:
            print("\n⚠️  警告: 系统配置验证未通过，请检查配置后重新验证")
            sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断初始化过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 初始化过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
