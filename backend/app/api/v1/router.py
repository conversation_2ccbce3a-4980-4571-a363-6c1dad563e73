"""
API v1 路由聚合器
统一管理v1版本的所有API路由
"""

from fastapi import APIRouter

from .files import router as files_router
from .health import router as health_router
from .monitoring import router as monitoring_router
from .search import router as search_router

# 创建v1版本的主路由
router = APIRouter(prefix="/v1", tags=["API v1"])

# 注册各个模块的路由
router.include_router(health_router)
router.include_router(monitoring_router)
router.include_router(search_router)
router.include_router(files_router)

# 未来可以在这里添加更多路由
# router.include_router(auth_router)
# router.include_router(users_router)
# router.include_router(channels_router)
# router.include_router(messages_router)
# router.include_router(ai_router)
# router.include_router(workflows_router)
