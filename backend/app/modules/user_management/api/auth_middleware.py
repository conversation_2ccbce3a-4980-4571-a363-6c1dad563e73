# user_management/api/auth_middleware.py - 认证中间件
"""
API权限验证中间件

功能：
1. JWT令牌验证
2. 用户身份认证
3. 权限验证
4. 角色验证
5. 访问日志记录
"""

import json
import logging
import time
from functools import wraps
from typing import Callable, List, Optional, Set, Union

from fastapi import HTTPException, Request, Response, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from starlette.middleware.base import BaseHTTPMiddleware

from ....database import get_db_session
from ....models.auth_models import AuthingUser
from ..services.auth_service import get_auth_service
from ..services.jwt_service import get_jwt_service
from ..services.permission_service import PermissionService

logger = logging.getLogger(__name__)


class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件 - 全局请求认证处理"""

    def __init__(
        self, app, exclude_paths: Set[str] = None, include_patterns: Set[str] = None
    ):
        super().__init__(app)

        # 默认排除的路径（不需要认证）
        self.exclude_paths = exclude_paths or {
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/api/v1/auth/reset-password",
        }

        # 需要包含的路径模式（需要认证）
        self.include_patterns = include_patterns or {
            "/api/",
        }

        self.jwt_service = get_jwt_service()
        self.auth_service = get_auth_service()

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        start_time = time.time()

        # 检查是否需要认证
        if not self._should_authenticate(request):
            response = await call_next(request)
            await self._log_request(request, response, start_time)
            return response

        try:
            # 提取和验证令牌
            token = self._extract_token(request)
            if not token:
                return self._create_error_response("缺少认证令牌", "MISSING_TOKEN")

            # 验证令牌并获取用户
            with get_db_session() as db:
                is_valid, user = await self.auth_service.validate_token(db, token)
                if not is_valid or not user:
                    return self._create_error_response("令牌无效", "INVALID_TOKEN")

            # 将用户信息添加到请求状态
            request.state.current_user = user
            request.state.access_token = token

            # 继续处理请求
            response = await call_next(request)
            await self._log_request(request, response, start_time, user)

            return response

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"认证中间件错误: {e}")
            return self._create_error_response("认证服务错误", "AUTH_SERVICE_ERROR")

    def _should_authenticate(self, request: Request) -> bool:
        """判断请求是否需要认证"""
        path = request.url.path

        # 检查排除路径
        if path in self.exclude_paths:
            return False

        # 检查包含模式
        for pattern in self.include_patterns:
            if path.startswith(pattern):
                return True

        return False

    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取令牌"""
        # 从Authorization头中提取
        auth_header = request.headers.get("authorization")
        if auth_header:
            token = self.jwt_service.extract_bearer_token(auth_header)
            if token:
                return token

        # 从Query参数中提取（可选，不推荐在生产环境使用）
        token = request.query_params.get("access_token")
        if token:
            return token

        return None

    def _create_error_response(self, message: str, error_code: str) -> Response:
        """创建错误响应"""
        content = {
            "success": False,
            "error_code": error_code,
            "error_message": message,
            "timestamp": time.time(),
        }

        return Response(
            content=json.dumps(content),
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"content-type": "application/json"},
        )

    async def _log_request(
        self,
        request: Request,
        response: Response,
        start_time: float,
        user: AuthingUser = None,
    ):
        """记录请求日志"""
        duration = time.time() - start_time

        log_data = {
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "user_id": user.id if user else None,
            "user_email": user.email if user else None,
            "ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
        }

        # 根据状态码选择日志级别
        if response.status_code >= 500:
            logger.error(f"API请求失败: {log_data}")
        elif response.status_code >= 400:
            logger.warning(f"API请求异常: {log_data}")
        else:
            logger.info(f"API请求成功: {log_data}")

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 优先从代理头中获取
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # 回退到客户端地址
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"


class RequireAuth:
    """认证装饰器 - 要求用户已登录"""

    def __init__(self, optional: bool = False):
        self.optional = optional
        self.security = HTTPBearer(auto_error=not optional)

    async def __call__(
        self, credentials: Optional[HTTPAuthorizationCredentials] = None
    ) -> Optional[AuthingUser]:
        """验证认证"""
        if not credentials and self.optional:
            return None

        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="缺少认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )

        jwt_service = get_jwt_service()
        auth_service = get_auth_service()

        try:
            with get_db_session() as db:
                is_valid, user = await auth_service.validate_token(
                    db, credentials.credentials
                )
                if not is_valid or not user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="令牌无效",
                        headers={"WWW-Authenticate": "Bearer"},
                    )

                return user
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"认证验证失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="认证服务错误"
            )


class RequirePermission:
    """权限装饰器 - 要求用户有指定权限"""

    def __init__(self, permission: Union[str, List[str]], require_all: bool = True):
        """
        Args:
            permission: 权限代码或权限代码列表
            require_all: 如果是多个权限，是否要求全部拥有（True）还是任一即可（False）
        """
        self.permissions = [permission] if isinstance(permission, str) else permission
        self.require_all = require_all
        self.auth_checker = RequireAuth()

    async def __call__(self, credentials: HTTPAuthorizationCredentials) -> AuthingUser:
        """验证权限"""
        # 首先验证认证
        user = await self.auth_checker(credentials)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户未认证"
            )

        # 验证权限
        try:
            with get_db_session() as db:
                permission_service = PermissionService()

                if self.require_all:
                    # 要求所有权限
                    for perm in self.permissions:
                        if not permission_service.check_user_permission(
                            db, user.id, perm
                        ):
                            raise HTTPException(
                                status_code=status.HTTP_403_FORBIDDEN,
                                detail=f"缺少权限: {perm}",
                            )
                else:
                    # 任一权限即可
                    has_permission = False
                    for perm in self.permissions:
                        if permission_service.check_user_permission(db, user.id, perm):
                            has_permission = True
                            break

                    if not has_permission:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail=f"缺少权限: {' 或 '.join(self.permissions)}",
                        )

                return user
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"权限验证失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="权限验证服务错误",
            )


class RequireRole:
    """角色装饰器 - 要求用户有指定角色"""

    def __init__(self, role: Union[str, List[str]], require_all: bool = False):
        """
        Args:
            role: 角色代码或角色代码列表
            require_all: 如果是多个角色，是否要求全部拥有（True）还是任一即可（False）
        """
        self.roles = [role] if isinstance(role, str) else role
        self.require_all = require_all
        self.auth_checker = RequireAuth()

    async def __call__(self, credentials: HTTPAuthorizationCredentials) -> AuthingUser:
        """验证角色"""
        # 首先验证认证
        user = await self.auth_checker(credentials)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户未认证"
            )

        # 验证角色
        try:
            from ..services.role_service import RoleService

            with get_db_session() as db:
                role_service = RoleService()

                if self.require_all:
                    # 要求所有角色
                    for role in self.roles:
                        if not role_service.check_user_has_role(db, user.id, role):
                            raise HTTPException(
                                status_code=status.HTTP_403_FORBIDDEN,
                                detail=f"缺少角色: {role}",
                            )
                else:
                    # 任一角色即可
                    has_role = False
                    for role in self.roles:
                        if role_service.check_user_has_role(db, user.id, role):
                            has_role = True
                            break

                    if not has_role:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail=f"缺少角色: {' 或 '.join(self.roles)}",
                        )

                return user
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"角色验证失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="角色验证服务错误",
            )


def permission_required(permission: Union[str, List[str]], require_all: bool = True):
    """权限装饰器函数"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里应该从请求中获取当前用户
            # 实际实现中需要根据框架调整
            return await func(*args, **kwargs)

        return wrapper

    return decorator


def role_required(role: Union[str, List[str]], require_all: bool = False):
    """角色装饰器函数"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里应该从请求中获取当前用户
            # 实际实现中需要根据框架调整
            return await func(*args, **kwargs)

        return wrapper

    return decorator


def auth_required(optional: bool = False):
    """认证装饰器函数"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里应该从请求中获取当前用户
            # 实际实现中需要根据框架调整
            return await func(*args, **kwargs)

        return wrapper

    return decorator


# 常用权限检查器实例
require_auth = RequireAuth()
require_auth_optional = RequireAuth(optional=True)

# 常用角色检查器
require_admin = RequireRole(["admin", "super_admin"], require_all=False)
require_super_admin = RequireRole("super_admin")
require_agent = RequireRole(["agent", "admin", "super_admin"], require_all=False)

# 常用权限检查器
require_user_read = RequirePermission("user:read")
require_user_write = RequirePermission(
    ["user:create", "user:update"], require_all=False
)
require_user_admin = RequirePermission(["user:delete", "user:block"], require_all=False)
