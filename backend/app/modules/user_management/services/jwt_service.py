# user_management/services/jwt_service.py - JWT令牌服务
"""
JWT令牌管理服务

功能：
1. 生成和验证JWT访问令牌
2. 生成和验证刷新令牌
3. 令牌加密和签名
4. 令牌黑名单管理
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Tuple

import jwt
from pydantic import BaseSettings

logger = logging.getLogger(__name__)


class JWTConfig(BaseSettings):
    """JWT配置"""

    # JWT密钥配置
    JWT_SECRET_KEY: str = "your-super-secret-jwt-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_REFRESH_SECRET_KEY: str = "your-refresh-secret-key-change-in-production"

    # 令牌过期时间（秒）
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 2  # 2小时
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7  # 7天

    # 发行者和受众
    JWT_ISSUER: str = "chaiguanjia"
    JWT_AUDIENCE: str = "chaiguanjia-users"

    # 其他配置
    JWT_VERIFY_SIGNATURE: bool = True
    JWT_VERIFY_EXP: bool = True
    JWT_REQUIRE_EXP: bool = True

    class Config:
        env_file = ".env"
        case_sensitive = True


class JWTService:
    """JWT令牌服务"""

    def __init__(self, config: JWTConfig = None, session_cache=None):
        self.config = config or JWTConfig()
        self.session_cache = session_cache
        self._fallback_blacklist = set()  # 降级时使用的内存黑名单

    def create_access_token(
        self,
        user_id: int,
        user_data: Dict = None,
        expires_delta: Optional[timedelta] = None,
    ) -> str:
        """创建访问令牌"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=self.config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            )

        payload = {
            "sub": str(user_id),  # 主题 (用户ID)
            "type": "access",
            "exp": expire,
            "iat": datetime.utcnow(),
            "iss": self.config.JWT_ISSUER,
            "aud": self.config.JWT_AUDIENCE,
        }

        # 添加用户数据
        if user_data:
            payload.update(
                {
                    "email": user_data.get("email"),
                    "name": user_data.get("name"),
                    "roles": user_data.get("roles", []),
                    "permissions": user_data.get("permissions", []),
                }
            )

        try:
            token = jwt.encode(
                payload, self.config.JWT_SECRET_KEY, algorithm=self.config.JWT_ALGORITHM
            )
            logger.debug(f"创建访问令牌成功: user_id={user_id}")
            return token
        except Exception as e:
            logger.error(f"创建访问令牌失败: {e}")
            raise JWTError(f"创建访问令牌失败: {str(e)}")

    def create_refresh_token(
        self,
        user_id: int,
        session_id: str = None,
        expires_delta: Optional[timedelta] = None,
    ) -> str:
        """创建刷新令牌"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=self.config.JWT_REFRESH_TOKEN_EXPIRE_DAYS
            )

        payload = {
            "sub": str(user_id),
            "type": "refresh",
            "exp": expire,
            "iat": datetime.utcnow(),
            "iss": self.config.JWT_ISSUER,
            "aud": self.config.JWT_AUDIENCE,
        }

        if session_id:
            payload["session_id"] = session_id

        try:
            token = jwt.encode(
                payload,
                self.config.JWT_REFRESH_SECRET_KEY,
                algorithm=self.config.JWT_ALGORITHM,
            )
            logger.debug(f"创建刷新令牌成功: user_id={user_id}")
            return token
        except Exception as e:
            logger.error(f"创建刷新令牌失败: {e}")
            raise JWTError(f"创建刷新令牌失败: {str(e)}")

    async def verify_access_token(self, token: str) -> Tuple[bool, Dict]:
        """验证访问令牌"""
        try:
            # 检查令牌是否在黑名单中
            if await self._is_token_blacklisted(token):
                return False, {"error": "令牌已被吊销"}

            payload = jwt.decode(
                token,
                self.config.JWT_SECRET_KEY,
                algorithms=[self.config.JWT_ALGORITHM],
                issuer=self.config.JWT_ISSUER,
                audience=self.config.JWT_AUDIENCE,
                options={
                    "verify_signature": self.config.JWT_VERIFY_SIGNATURE,
                    "verify_exp": self.config.JWT_VERIFY_EXP,
                    "require_exp": self.config.JWT_REQUIRE_EXP,
                },
            )

            # 验证令牌类型
            if payload.get("type") != "access":
                return False, {"error": "令牌类型错误"}

            logger.debug(f"访问令牌验证成功: user_id={payload.get('sub')}")
            return True, payload

        except jwt.ExpiredSignatureError:
            logger.warning("访问令牌已过期")
            return False, {"error": "令牌已过期"}
        except jwt.InvalidTokenError as e:
            logger.warning(f"访问令牌无效: {e}")
            return False, {"error": "令牌无效"}
        except Exception as e:
            logger.error(f"访问令牌验证失败: {e}")
            return False, {"error": "令牌验证失败"}

    async def verify_refresh_token(self, token: str) -> Tuple[bool, Dict]:
        """验证刷新令牌"""
        try:
            # 检查令牌是否在黑名单中
            if await self._is_token_blacklisted(token):
                return False, {"error": "令牌已被吊销"}

            payload = jwt.decode(
                token,
                self.config.JWT_REFRESH_SECRET_KEY,
                algorithms=[self.config.JWT_ALGORITHM],
                issuer=self.config.JWT_ISSUER,
                audience=self.config.JWT_AUDIENCE,
                options={
                    "verify_signature": self.config.JWT_VERIFY_SIGNATURE,
                    "verify_exp": self.config.JWT_VERIFY_EXP,
                    "require_exp": self.config.JWT_REQUIRE_EXP,
                },
            )

            # 验证令牌类型
            if payload.get("type") != "refresh":
                return False, {"error": "令牌类型错误"}

            logger.debug(f"刷新令牌验证成功: user_id={payload.get('sub')}")
            return True, payload

        except jwt.ExpiredSignatureError:
            logger.warning("刷新令牌已过期")
            return False, {"error": "令牌已过期"}
        except jwt.InvalidTokenError as e:
            logger.warning(f"刷新令牌无效: {e}")
            return False, {"error": "令牌无效"}
        except Exception as e:
            logger.error(f"刷新令牌验证失败: {e}")
            return False, {"error": "令牌验证失败"}

    def decode_token_payload(self, token: str) -> Optional[Dict]:
        """解码令牌载荷（不验证签名，用于获取过期令牌信息）"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            return payload
        except Exception as e:
            logger.error(f"解码令牌载荷失败: {e}")
            return None

    def get_user_id_from_token(self, token: str) -> Optional[int]:
        """从令牌中获取用户ID"""
        is_valid, payload = self.verify_access_token(token)
        if is_valid:
            try:
                return int(payload.get("sub"))
            except (ValueError, TypeError):
                return None
        return None

    async def revoke_token(self, token: str) -> bool:
        """吊销令牌（添加到黑名单）"""
        try:
            if self.session_cache:
                return await self.session_cache.add_token_to_blacklist(token)
            else:
                # 降级到内存黑名单
                token_hash = self._hash_token(token)
                self._fallback_blacklist.add(token_hash)
                logger.warning("Redis不可用，使用内存黑名单")
                return True
        except Exception as e:
            logger.error(f"吊销令牌失败: {e}")
            return False

    def revoke_user_tokens(self, user_id: int) -> bool:
        """吊销用户的所有令牌（这里简化实现，实际应该查询数据库）"""
        # 在实际应用中，应该查询用户会话表，将所有相关令牌加入黑名单
        logger.info(f"用户所有令牌已吊销: user_id={user_id}")
        return True

    def clean_expired_blacklist(self):
        """清理过期的黑名单令牌（简化实现）"""
        # 在实际应用中，应该定期清理已过期的令牌
        logger.info("黑名单清理完成")

    def _hash_token(self, token: str) -> str:
        """对令牌进行哈希处理"""
        return hashlib.sha256(token.encode()).hexdigest()

    async def _is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        try:
            if self.session_cache:
                return await self.session_cache.is_token_blacklisted(token)
            else:
                # 降级到内存黑名单
                token_hash = self._hash_token(token)
                return token_hash in self._fallback_blacklist
        except Exception as e:
            logger.error(f"检查令牌黑名单失败: {e}")
            # 安全起见，检查失败时假设token有效
            return False

    def create_token_pair(
        self, user_id: int, user_data: Dict = None, session_id: str = None
    ) -> Dict[str, Any]:
        """创建令牌对（访问令牌 + 刷新令牌）"""
        try:
            access_token = self.create_access_token(user_id, user_data)
            refresh_token = self.create_refresh_token(user_id, session_id)

            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "Bearer",
                "expires_in": self.config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "refresh_expires_in": self.config.JWT_REFRESH_TOKEN_EXPIRE_DAYS
                * 24
                * 3600,
            }
        except Exception as e:
            logger.error(f"创建令牌对失败: {e}")
            raise JWTError(f"创建令牌对失败: {str(e)}")

    def refresh_access_token(
        self, refresh_token: str, user_data: Dict = None
    ) -> Dict[str, Any]:
        """使用刷新令牌获取新的访问令牌"""
        is_valid, payload = self.verify_refresh_token(refresh_token)
        if not is_valid:
            raise JWTError(f"刷新令牌无效: {payload.get('error')}")

        user_id = int(payload.get("sub"))

        try:
            # 创建新的访问令牌
            access_token = self.create_access_token(user_id, user_data)

            return {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": self.config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            }
        except Exception as e:
            logger.error(f"刷新访问令牌失败: {e}")
            raise JWTError(f"刷新访问令牌失败: {str(e)}")

    def extract_bearer_token(self, authorization_header: str) -> Optional[str]:
        """从Authorization头中提取Bearer令牌"""
        if not authorization_header:
            return None

        parts = authorization_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            return None

        return parts[1]

    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """获取令牌过期时间"""
        payload = self.decode_token_payload(token)
        if payload and "exp" in payload:
            return datetime.fromtimestamp(payload["exp"])
        return None

    def is_token_expired(self, token: str) -> bool:
        """检查令牌是否过期"""
        expiry = self.get_token_expiry(token)
        if expiry:
            return datetime.utcnow() > expiry
        return True


class JWTError(Exception):
    """JWT服务错误"""

    pass


# 创建全局JWT服务实例
_jwt_service = None


def get_jwt_service() -> JWTService:
    """获取JWT服务实例"""
    global _jwt_service
    if _jwt_service is None:
        _jwt_service = JWTService()
    return _jwt_service
