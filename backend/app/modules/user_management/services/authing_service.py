# user_management/services/authing_service.py - Authing集成服务
"""
Authing身份认证服务集成

主要功能：
1. 用户注册、登录、登出
2. 用户信息同步
3. 令牌验证和刷新
4. 用户管理操作
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import httpx
from pydantic import BaseSettings

logger = logging.getLogger(__name__)


class AuthingConfig(BaseSettings):
    """Authing配置"""

    # Authing基础配置
    AUTHING_USER_POOL_ID: str = ""
    AUTHING_APP_ID: str = ""
    AUTHING_APP_SECRET: str = ""
    AUTHING_APP_HOST: str = ""
    AUTHING_PROTOCOL: str = "https"

    # API配置
    AUTHING_API_BASE_URL: str = "https://core.authing.cn"
    AUTHING_TIMEOUT: int = 30
    AUTHING_RETRY_TIMES: int = 3

    # 同步配置
    AUTHING_SYNC_ENABLED: bool = True
    AUTHING_SYNC_INTERVAL: int = 3600  # 1小时

    class Config:
        env_file = ".env"
        case_sensitive = True


class AuthingService:
    """Authing身份认证服务"""

    def __init__(self, config: AuthingConfig = None):
        self.config = config or AuthingConfig()
        self.client = httpx.AsyncClient(
            base_url=self.config.AUTHING_API_BASE_URL,
            timeout=self.config.AUTHING_TIMEOUT,
            headers={
                "Content-Type": "application/json",
                "x-authing-userpool-id": self.config.AUTHING_USER_POOL_ID,
                "x-authing-app-id": self.config.AUTHING_APP_ID,
            },
        )

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    async def _make_request(
        self, method: str, endpoint: str, data: Dict = None, headers: Dict = None
    ) -> Dict:
        """发起HTTP请求"""
        try:
            request_headers = self.client.headers.copy()
            if headers:
                request_headers.update(headers)

            response = await self.client.request(
                method=method, url=endpoint, json=data, headers=request_headers
            )

            response.raise_for_status()
            result = response.json()

            if result.get("code") != 200:
                raise AuthingAPIError(
                    f"Authing API错误: {result.get('message', '未知错误')}",
                    result.get("code"),
                    result,
                )

            return result.get("data", {})

        except httpx.HTTPStatusError as e:
            logger.error(f"Authing API HTTP错误: {e}")
            raise AuthingAPIError(f"HTTP错误: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Authing API请求错误: {e}")
            raise AuthingAPIError(f"请求错误: {str(e)}")
        except Exception as e:
            logger.error(f"Authing API未知错误: {e}")
            raise AuthingAPIError(f"未知错误: {str(e)}")

    async def register_user(
        self,
        email: str,
        password: str,
        name: str,
        phone: str = None,
        username: str = None,
    ) -> Dict:
        """注册用户"""
        data = {
            "email": email,
            "password": password,
            "name": name,
        }

        if phone:
            data["phone"] = phone
        if username:
            data["username"] = username

        try:
            result = await self._make_request("POST", "/api/v2/users", data)
            logger.info(f"用户注册成功: {email}")
            return result
        except Exception as e:
            logger.error(f"用户注册失败: {email}, 错误: {e}")
            raise

    async def login_user(
        self, email: str, password: str, device_info: Dict = None
    ) -> Dict:
        """用户登录"""
        data = {
            "email": email,
            "password": password,
            "clientIp": device_info.get("ip") if device_info else None,
            "userAgent": device_info.get("user_agent") if device_info else None,
        }

        # 移除None值
        data = {k: v for k, v in data.items() if v is not None}

        try:
            result = await self._make_request("POST", "/api/v2/signin", data)
            logger.info(f"用户登录成功: {email}")
            return result
        except Exception as e:
            logger.error(f"用户登录失败: {email}, 错误: {e}")
            raise

    async def logout_user(self, access_token: str) -> bool:
        """用户登出"""
        try:
            headers = {"Authorization": f"Bearer {access_token}"}
            await self._make_request("POST", "/api/v2/logout", headers=headers)
            logger.info("用户登出成功")
            return True
        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return False

    async def get_user_info(
        self, access_token: str = None, user_id: str = None
    ) -> Dict:
        """获取用户信息"""
        if access_token:
            headers = {"Authorization": f"Bearer {access_token}"}
            endpoint = "/api/v2/users/me"
        elif user_id:
            headers = {"Authorization": f"Bearer {self._get_management_token()}"}
            endpoint = f"/api/v2/users/{user_id}"
        else:
            raise ValueError("需要提供access_token或user_id")

        try:
            result = await self._make_request("GET", endpoint, headers=headers)
            return result
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            raise

    async def update_user_info(self, user_id: str, updates: Dict) -> Dict:
        """更新用户信息"""
        headers = {"Authorization": f"Bearer {self._get_management_token()}"}

        try:
            result = await self._make_request(
                "PATCH", f"/api/v2/users/{user_id}", data=updates, headers=headers
            )
            logger.info(f"用户信息更新成功: {user_id}")
            return result
        except Exception as e:
            logger.error(f"用户信息更新失败: {user_id}, 错误: {e}")
            raise

    async def validate_token(self, access_token: str) -> Tuple[bool, Dict]:
        """验证访问令牌"""
        try:
            headers = {"Authorization": f"Bearer {access_token}"}
            result = await self._make_request(
                "GET", "/api/v2/introspect", headers=headers
            )

            is_valid = result.get("active", False)
            return is_valid, result
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return False, {}

    async def refresh_token(self, refresh_token: str) -> Dict:
        """刷新访问令牌"""
        data = {
            "refresh_token": refresh_token,
            "client_id": self.config.AUTHING_APP_ID,
            "client_secret": self.config.AUTHING_APP_SECRET,
            "grant_type": "refresh_token",
        }

        try:
            result = await self._make_request("POST", "/oidc/token", data)
            logger.info("令牌刷新成功")
            return result
        except Exception as e:
            logger.error(f"令牌刷新失败: {e}")
            raise

    async def list_users(
        self, page: int = 1, limit: int = 10, filters: Dict = None
    ) -> Dict:
        """获取用户列表"""
        headers = {"Authorization": f"Bearer {self._get_management_token()}"}

        params = {
            "page": page,
            "limit": limit,
        }

        if filters:
            params.update(filters)

        try:
            result = await self._make_request("GET", "/api/v2/users", headers=headers)
            return result
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            raise

    async def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        headers = {"Authorization": f"Bearer {self._get_management_token()}"}

        try:
            await self._make_request(
                "DELETE", f"/api/v2/users/{user_id}", headers=headers
            )
            logger.info(f"用户删除成功: {user_id}")
            return True
        except Exception as e:
            logger.error(f"用户删除失败: {user_id}, 错误: {e}")
            return False

    async def send_email_verification(self, email: str) -> bool:
        """发送邮箱验证"""
        data = {"email": email}

        try:
            await self._make_request("POST", "/api/v2/email/send", data)
            logger.info(f"邮箱验证发送成功: {email}")
            return True
        except Exception as e:
            logger.error(f"邮箱验证发送失败: {email}, 错误: {e}")
            return False

    async def verify_email(self, email: str, code: str) -> bool:
        """验证邮箱"""
        data = {"email": email, "code": code}

        try:
            await self._make_request("POST", "/api/v2/email/verify", data)
            logger.info(f"邮箱验证成功: {email}")
            return True
        except Exception as e:
            logger.error(f"邮箱验证失败: {email}, 错误: {e}")
            return False

    async def reset_password(self, email: str) -> bool:
        """发送重置密码邮件"""
        data = {"email": email}

        try:
            await self._make_request("POST", "/api/v2/password/reset", data)
            logger.info(f"重置密码邮件发送成功: {email}")
            return True
        except Exception as e:
            logger.error(f"重置密码邮件发送失败: {email}, 错误: {e}")
            return False

    async def change_password(
        self, user_id: str, old_password: str, new_password: str
    ) -> bool:
        """修改密码"""
        data = {"newPassword": new_password, "oldPassword": old_password}
        headers = {"Authorization": f"Bearer {self._get_management_token()}"}

        try:
            await self._make_request(
                "POST",
                f"/api/v2/users/{user_id}/password/reset",
                data=data,
                headers=headers,
            )
            logger.info(f"密码修改成功: {user_id}")
            return True
        except Exception as e:
            logger.error(f"密码修改失败: {user_id}, 错误: {e}")
            return False

    def _get_management_token(self) -> str:
        """获取管理令牌（简化实现，实际应该缓存）"""
        # 这里应该实现获取管理API访问令牌的逻辑
        # 为了简化，暂时使用应用密钥
        return self.config.AUTHING_APP_SECRET

    async def sync_user_to_local(self, authing_user: Dict) -> Dict:
        """将Authing用户数据同步到本地数据库格式"""
        return {
            "authing_user_id": authing_user.get("id"),
            "authing_user_pool_id": authing_user.get("userPoolId"),
            "email": authing_user.get("email"),
            "username": authing_user.get("username"),
            "phone": authing_user.get("phone"),
            "name": authing_user.get("name") or authing_user.get("nickname"),
            "nickname": authing_user.get("nickname"),
            "avatar": authing_user.get("photo"),
            "is_email_verified": authing_user.get("emailVerified", False),
            "is_phone_verified": authing_user.get("phoneVerified", False),
            "gender": authing_user.get("gender"),
            "birthdate": self._parse_datetime(authing_user.get("birthdate")),
            "country": authing_user.get("country"),
            "province": authing_user.get("province"),
            "city": authing_user.get("city"),
            "address": authing_user.get("address"),
            "postal_code": authing_user.get("postalCode"),
            "language": authing_user.get("locale", "zh-CN"),
            "timezone": authing_user.get("zoneinfo", "Asia/Shanghai"),
            "custom_data": authing_user.get("customData", {}),
            "metadata": {
                "authing_created_at": authing_user.get("createdAt"),
                "authing_updated_at": authing_user.get("updatedAt"),
                "last_login": authing_user.get("lastLogin"),
                "login_count": authing_user.get("loginsCount", 0),
            },
            "authing_sync_at": datetime.now(),
            "sync_status": "success",
        }

    def _parse_datetime(self, date_str: str) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not date_str:
            return None
        try:
            # 尝试多种日期格式
            formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%d",
                "%Y-%m-%d %H:%M:%S",
            ]
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return None
        except Exception:
            return None


class AuthingAPIError(Exception):
    """Authing API错误"""

    def __init__(self, message: str, code: int = None, response: Dict = None):
        self.message = message
        self.code = code
        self.response = response
        super().__init__(self.message)


# 创建全局Authing服务实例
_authing_service = None


async def get_authing_service() -> AuthingService:
    """获取Authing服务实例"""
    global _authing_service
    if _authing_service is None:
        _authing_service = AuthingService()
    return _authing_service
