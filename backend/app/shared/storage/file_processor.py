"""
文件处理模块

提供文件处理功能，包括：
- 图片压缩和缩略图生成
- 文件类型检测
- 文件元数据提取
- 图片格式转换
"""

import hashlib
import io
import logging
import mimetypes
from typing import Dict, Optional, Tuple

import magic
from app.core.config import get_settings
from PIL import ExifTags, Image, ImageOps

from .storage_models import FileMetadata

logger = logging.getLogger(__name__)


class FileProcessor:
    """文件处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 支持的图片格式
        self.supported_image_formats = {"JPEG", "PNG", "GIF", "BMP", "WEBP", "TIFF"}

        # MIME类型映射
        self.mime_type_map = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "bmp": "image/bmp",
            "webp": "image/webp",
            "svg": "image/svg+xml",
            "pdf": "application/pdf",
            "doc": "application/msword",
            "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "xls": "application/vnd.ms-excel",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "ppt": "application/vnd.ms-powerpoint",
            "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "txt": "text/plain",
            "csv": "text/csv",
            "zip": "application/zip",
            "rar": "application/x-rar-compressed",
            "7z": "application/x-7z-compressed",
            "mp4": "video/mp4",
            "mp3": "audio/mpeg",
            "avi": "video/x-msvideo",
            "mov": "video/quicktime",
        }

    def get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        if not filename or "." not in filename:
            return ""
        return filename.rsplit(".", 1)[1].lower()

    def get_mime_type(self, filename: str, file_data: Optional[bytes] = None) -> str:
        """
        获取MIME类型

        Args:
            filename: 文件名
            file_data: 文件数据（用于更准确的检测）

        Returns:
            MIME类型
        """
        # 先通过扩展名确定
        extension = self.get_file_extension(filename)
        mime_type = self.mime_type_map.get(extension)

        if mime_type:
            return mime_type

        # 通过文件内容检测
        if file_data:
            try:
                detected_type = magic.from_buffer(file_data, mime=True)
                return detected_type
            except Exception as e:
                logger.warning(f"Failed to detect MIME type from content: {e}")

        # 使用Python内置方法
        guessed_type, _ = mimetypes.guess_type(filename)
        return guessed_type or "application/octet-stream"

    def calculate_file_hash(self, file_data: bytes) -> str:
        """计算文件哈希值"""
        return hashlib.sha256(file_data).hexdigest()

    def is_image_file(self, filename: str) -> bool:
        """检查是否为图片文件"""
        return self.settings.is_image_file(filename)

    def validate_file_size(self, file_size: int) -> bool:
        """验证文件大小"""
        return file_size <= self.settings.max_file_size

    def validate_file_extension(self, filename: str) -> bool:
        """验证文件扩展名"""
        extension = self.get_file_extension(filename)
        allowed_extensions = self.settings.get_allowed_extensions()
        return extension in allowed_extensions

    def extract_image_metadata(self, image: Image.Image) -> Dict:
        """
        提取图片元数据

        Args:
            image: PIL Image对象

        Returns:
            元数据字典
        """
        metadata = {
            "width": image.width,
            "height": image.height,
            "format": image.format,
            "mode": image.mode,
        }

        # 提取EXIF信息
        try:
            exif_data = image._getexif()
            if exif_data:
                exif = {}
                for tag_id, value in exif_data.items():
                    tag = ExifTags.TAGS.get(tag_id, tag_id)
                    exif[tag] = value
                metadata["exif"] = exif
        except Exception as e:
            logger.debug(f"Failed to extract EXIF data: {e}")

        return metadata

    def process_image(
        self,
        image_data: bytes,
        max_dimension: Optional[int] = None,
        quality: Optional[int] = None,
    ) -> Tuple[bytes, Dict]:
        """
        处理图片（压缩、旋转等）

        Args:
            image_data: 原始图片数据
            max_dimension: 最大尺寸
            quality: 压缩质量

        Returns:
            (处理后的图片数据, 元数据)
        """
        try:
            # 打开图片
            image = Image.open(io.BytesIO(image_data))

            # 提取元数据
            metadata = self.extract_image_metadata(image)

            # 自动旋转（根据EXIF信息）
            image = ImageOps.exif_transpose(image)

            # 转换为RGB模式（如果不是）
            if image.mode in ("RGBA", "LA", "P"):
                # 创建白色背景
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "P":
                    image = image.convert("RGBA")
                background.paste(
                    image, mask=image.split()[-1] if image.mode == "RGBA" else None
                )
                image = background
            elif image.mode != "RGB":
                image = image.convert("RGB")

            # 调整尺寸
            max_dim = max_dimension or self.settings.max_image_dimension
            if max(image.width, image.height) > max_dim:
                ratio = max_dim / max(image.width, image.height)
                new_size = (int(image.width * ratio), int(image.height * ratio))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            # 保存压缩后的图片
            output_buffer = io.BytesIO()
            quality_setting = quality or self.settings.image_quality

            image.save(
                output_buffer, format="JPEG", quality=quality_setting, optimize=True
            )

            processed_data = output_buffer.getvalue()

            # 更新元数据
            metadata.update(
                {
                    "processed_width": image.width,
                    "processed_height": image.height,
                    "processed_size": len(processed_data),
                    "quality": quality_setting,
                }
            )

            return processed_data, metadata

        except Exception as e:
            logger.error(f"Error processing image: {e}")
            # 如果处理失败，返回原始数据
            return image_data, {"error": str(e)}

    def generate_thumbnail(
        self,
        image_data: bytes,
        size: Optional[Tuple[int, int]] = None,
        quality: Optional[int] = None,
    ) -> bytes:
        """
        生成缩略图

        Args:
            image_data: 原始图片数据
            size: 缩略图尺寸
            quality: 压缩质量

        Returns:
            缩略图数据
        """
        try:
            # 打开图片
            image = Image.open(io.BytesIO(image_data))

            # 自动旋转
            image = ImageOps.exif_transpose(image)

            # 转换为RGB模式
            if image.mode in ("RGBA", "LA", "P"):
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "P":
                    image = image.convert("RGBA")
                background.paste(
                    image, mask=image.split()[-1] if image.mode == "RGBA" else None
                )
                image = background
            elif image.mode != "RGB":
                image = image.convert("RGB")

            # 设置缩略图尺寸
            thumbnail_size = size or self.settings.get_thumbnail_size()

            # 生成缩略图（保持宽高比）
            image.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)

            # 创建正方形缩略图（居中裁剪）
            thumb_width, thumb_height = thumbnail_size
            img_width, img_height = image.size

            if img_width != thumb_width or img_height != thumb_height:
                # 创建白色背景
                thumbnail = Image.new("RGB", thumbnail_size, (255, 255, 255))

                # 计算居中位置
                x = (thumb_width - img_width) // 2
                y = (thumb_height - img_height) // 2

                # 粘贴图片
                thumbnail.paste(image, (x, y))
                image = thumbnail

            # 保存缩略图
            output_buffer = io.BytesIO()
            quality_setting = quality or self.settings.image_quality

            image.save(
                output_buffer, format="JPEG", quality=quality_setting, optimize=True
            )

            return output_buffer.getvalue()

        except Exception as e:
            logger.error(f"Error generating thumbnail: {e}")
            raise

    def create_file_metadata(
        self,
        file_id: str,
        filename: str,
        file_data: bytes,
        file_path: str,
        owner_id: Optional[str] = None,
        **kwargs,
    ) -> FileMetadata:
        """
        创建文件元数据对象

        Args:
            file_id: 文件ID
            filename: 文件名
            file_data: 文件数据
            file_path: 存储路径
            owner_id: 所有者ID
            **kwargs: 额外参数

        Returns:
            文件元数据对象
        """
        # 基本信息
        file_size = len(file_data)
        file_extension = self.get_file_extension(filename)
        file_type = self.get_mime_type(filename, file_data)
        content_hash = self.calculate_file_hash(file_data)

        # 创建元数据
        metadata = FileMetadata(
            id=file_id,
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_type,
            file_extension=file_extension,
            content_hash=content_hash,
            owner_id=owner_id,
            **kwargs,
        )

        # 如果是图片，提取图片信息
        if self.is_image_file(filename):
            try:
                image = Image.open(io.BytesIO(file_data))
                image_metadata = self.extract_image_metadata(image)

                metadata.image_width = image_metadata.get("width")
                metadata.image_height = image_metadata.get("height")
                metadata.metadata.update(image_metadata)

            except Exception as e:
                logger.warning(f"Failed to extract image metadata: {e}")

        return metadata

    def get_file_icon(self, filename: str) -> str:
        """
        根据文件类型获取图标

        Args:
            filename: 文件名

        Returns:
            图标类型
        """
        extension = self.get_file_extension(filename)

        icon_map = {
            # 图片
            "jpg": "image",
            "jpeg": "image",
            "png": "image",
            "gif": "image",
            "bmp": "image",
            "webp": "image",
            "svg": "image",
            # 文档
            "pdf": "pdf",
            "doc": "word",
            "docx": "word",
            "xls": "excel",
            "xlsx": "excel",
            "ppt": "powerpoint",
            "pptx": "powerpoint",
            "txt": "text",
            "csv": "text",
            # 压缩包
            "zip": "archive",
            "rar": "archive",
            "7z": "archive",
            # 媒体
            "mp4": "video",
            "avi": "video",
            "mov": "video",
            "mp3": "audio",
        }

        return icon_map.get(extension, "file")


# 全局处理器实例
file_processor: Optional[FileProcessor] = None


def get_file_processor() -> FileProcessor:
    """获取文件处理器实例"""
    global file_processor

    if file_processor is None:
        file_processor = FileProcessor()

    return file_processor
