#!/usr/bin/env python3
"""
性能测试执行脚本
协调运行所有性能测试并生成综合报告
"""

import argparse
import asyncio
import json
import os
import subprocess
import sys
import threading
import time
from datetime import datetime
from typing import Any, Dict, Optional

from benchmark_test import BenchmarkTester
from database_performance_test import DatabasePerformanceTester
from system_monitor import SystemResourceMonitor


class PerformanceTestRunner:
    """性能测试运行器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.results = {}
        self.start_time = None
        self.end_time = None
        self.system_monitor = None
        self.monitor_thread = None

    def prepare_test_environment(self) -> bool:
        """准备测试环境"""
        print("=" * 60)
        print("准备性能测试环境")
        print("=" * 60)

        # 检查API服务是否运行
        api_url = self.config.get("api_url", "http://localhost:8000")
        print(f"检查API服务: {api_url}")

        try:
            import requests

            response = requests.get(f"{api_url}/health/", timeout=10)
            if response.status_code == 200:
                print("✅ API服务运行正常")
            else:
                print(f"❌ API服务响应异常: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到API服务: {e}")
            return False

        # 检查数据库连接
        postgres_url = self.config.get(
            "postgres_url", "postgresql://admin:password@localhost:5432/chaiguanjia"
        )
        redis_url = self.config.get("redis_url", "redis://localhost:6379/0")

        print(f"检查数据库连接...")
        # 这里可以添加数据库连接检查逻辑
        print("✅ 数据库连接检查通过")

        # 创建结果目录
        results_dir = self.config.get("results_dir", "./performance_results")
        os.makedirs(results_dir, exist_ok=True)
        print(f"✅ 结果目录准备完成: {results_dir}")

        # 清理之前的测试数据
        self._cleanup_test_data()

        print("✅ 测试环境准备完成\n")
        return True

    def _cleanup_test_data(self):
        """清理测试数据"""
        print("清理测试数据...")
        # 这里可以添加清理逻辑，比如清理Redis测试键等
        time.sleep(1)
        print("✅ 测试数据清理完成")

    def start_system_monitoring(self):
        """启动系统监控"""
        print("启动系统资源监控...")

        self.system_monitor = SystemResourceMonitor(interval=1.0)

        # 在单独线程中运行监控
        def monitor_worker():
            results_dir = self.config.get("results_dir", "./performance_results")
            monitor_file = os.path.join(results_dir, "system_monitor.json")

            self.system_monitor.start_monitoring(
                output_file=monitor_file, real_time=False
            )

        self.monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self.monitor_thread.start()

        # 等待监控器启动
        time.sleep(2)
        print("✅ 系统监控已启动\n")

    def stop_system_monitoring(self):
        """停止系统监控"""
        if self.system_monitor:
            print("停止系统资源监控...")
            self.system_monitor.stop_monitoring()

            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)

            print("✅ 系统监控已停止")

    async def run_api_benchmark_tests(self) -> Dict:
        """运行API基准测试"""
        print("=" * 60)
        print("开始API基准性能测试")
        print("=" * 60)

        api_url = self.config.get("api_url", "http://localhost:8000")
        tester = BenchmarkTester(api_url)

        try:
            results = await tester.comprehensive_test()

            # 保存详细结果
            results_dir = self.config.get("results_dir", "./performance_results")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            with open(
                os.path.join(results_dir, f"api_benchmark_{timestamp}.json"), "w"
            ) as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            return results

        except Exception as e:
            print(f"❌ API基准测试失败: {e}")
            return {"error": str(e)}

    async def run_database_performance_tests(self) -> Dict:
        """运行数据库性能测试"""
        print("=" * 60)
        print("开始数据库性能测试")
        print("=" * 60)

        postgres_url = self.config.get(
            "postgres_url", "postgresql://admin:password@localhost:5432/chaiguanjia"
        )
        redis_url = self.config.get("redis_url", "redis://localhost:6379/0")

        tester = DatabasePerformanceTester(postgres_url, redis_url)

        try:
            results = await tester.comprehensive_database_test()

            # 保存详细结果
            results_dir = self.config.get("results_dir", "./performance_results")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            with open(
                os.path.join(results_dir, f"database_performance_{timestamp}.json"), "w"
            ) as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            return results

        except Exception as e:
            print(f"❌ 数据库性能测试失败: {e}")
            return {"error": str(e)}

    def run_locust_stress_test(self) -> Dict:
        """运行Locust压力测试"""
        print("=" * 60)
        print("开始Locust压力测试")
        print("=" * 60)

        api_url = self.config.get("api_url", "http://localhost:8000")
        users = self.config.get("locust_users", 1000)
        spawn_rate = self.config.get("locust_spawn_rate", 50)
        duration = self.config.get("locust_duration", 300)  # 5分钟

        results_dir = self.config.get("results_dir", "./performance_results")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 准备Locust命令
        locust_file = os.path.join(os.path.dirname(__file__), "locustfile.py")
        cmd = [
            "locust",
            "-f",
            locust_file,
            "--host",
            api_url,
            "--users",
            str(users),
            "--spawn-rate",
            str(spawn_rate),
            "--run-time",
            f"{duration}s",
            "--headless",
            "--csv",
            os.path.join(results_dir, f"locust_results_{timestamp}"),
            "--html",
            os.path.join(results_dir, f"locust_report_{timestamp}.html"),
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print(f"用户数: {users}, 生成速率: {spawn_rate}/s, 持续时间: {duration}s")

        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=duration + 60
            )

            if result.returncode == 0:
                print("✅ Locust压力测试完成")

                # 解析结果
                return self._parse_locust_results(results_dir, timestamp, result.stdout)
            else:
                print(f"❌ Locust测试失败: {result.stderr}")
                return {"error": result.stderr}

        except subprocess.TimeoutExpired:
            print("❌ Locust测试超时")
            return {"error": "timeout"}
        except FileNotFoundError:
            print("❌ 未找到locust命令，请安装Locust")
            return {"error": "locust not found"}

    def _parse_locust_results(
        self, results_dir: str, timestamp: str, stdout: str
    ) -> Dict:
        """解析Locust结果"""
        result = {"stdout": stdout}

        # 尝试读取CSV结果文件
        stats_file = os.path.join(results_dir, f"locust_results_{timestamp}_stats.csv")
        if os.path.exists(stats_file):
            try:
                with open(stats_file, "r") as f:
                    lines = f.readlines()
                    if len(lines) > 1:
                        # 解析统计数据
                        headers = lines[0].strip().split(",")
                        data = lines[1].strip().split(",")

                        stats = {}
                        for i, header in enumerate(headers):
                            if i < len(data):
                                try:
                                    stats[header] = (
                                        float(data[i])
                                        if "." in data[i]
                                        else int(data[i])
                                    )
                                except ValueError:
                                    stats[header] = data[i]

                        result["stats"] = stats
            except Exception as e:
                print(f"解析Locust结果失败: {e}")

        return result

    def analyze_performance_results(self) -> Dict:
        """分析性能测试结果"""
        print("=" * 60)
        print("分析性能测试结果")
        print("=" * 60)

        analysis = {
            "timestamp": datetime.now().isoformat(),
            "test_duration_seconds": (
                (self.end_time - self.start_time)
                if self.end_time and self.start_time
                else 0
            ),
            "summary": {},
            "compliance": {},
            "recommendations": [],
        }

        # 分析API性能
        api_results = self.results.get("api_benchmark", {})
        if api_results and "error" not in api_results:
            analysis["summary"]["api"] = self._analyze_api_performance(api_results)

        # 分析数据库性能
        db_results = self.results.get("database_performance", {})
        if db_results and "error" not in db_results:
            analysis["summary"]["database"] = self._analyze_database_performance(
                db_results
            )

        # 分析Locust结果
        locust_results = self.results.get("locust_stress", {})
        if locust_results and "error" not in locust_results:
            analysis["summary"]["stress"] = self._analyze_locust_performance(
                locust_results
            )

        # 合规性检查
        analysis["compliance"] = self._check_compliance(analysis["summary"])

        # 生成优化建议
        analysis["recommendations"] = self._generate_recommendations(analysis)

        return analysis

    def _analyze_api_performance(self, results: Dict) -> Dict:
        """分析API性能"""
        summary = {}

        # 查找高负载测试结果
        for endpoint, endpoint_results in results.items():
            high_load = endpoint_results.get("高负载_async", {})
            if high_load and "error" not in high_load:
                qps = high_load.get("requests_per_second", 0)
                avg_time = high_load.get("mean_response_time", 0) * 1000
                error_rate = high_load.get("error_rate", 0)

                summary[endpoint] = {
                    "qps": qps,
                    "avg_response_time_ms": avg_time,
                    "error_rate": error_rate,
                    "performance_grade": self._grade_api_performance(
                        qps, avg_time, error_rate
                    ),
                }

        return summary

    def _analyze_database_performance(self, results: Dict) -> Dict:
        """分析数据库性能"""
        summary = {}

        # PostgreSQL分析
        postgres_results = results.get("postgresql", {})
        if postgres_results:
            high_load = postgres_results.get("高负载_async", {})
            if high_load and "error" not in high_load:
                qps = high_load.get("operations_per_second", 0)
                avg_time = high_load.get("avg_response_time_ms", 0)

                summary["postgresql"] = {
                    "qps": qps,
                    "avg_response_time_ms": avg_time,
                    "meets_target": qps >= 10000,
                    "performance_grade": (
                        "A" if qps >= 10000 else "B" if qps >= 5000 else "C"
                    ),
                }

        # Redis分析
        redis_results = results.get("redis", {})
        if redis_results:
            high_load = redis_results.get("高负载_async", {})
            if high_load and "error" not in high_load:
                qps = high_load.get("operations_per_second", 0)
                avg_time = high_load.get("avg_response_time_ms", 0)

                summary["redis"] = {
                    "qps": qps,
                    "avg_response_time_ms": avg_time,
                    "performance_grade": (
                        "A" if qps >= 50000 else "B" if qps >= 20000 else "C"
                    ),
                }

        return summary

    def _analyze_locust_performance(self, results: Dict) -> Dict:
        """分析Locust压力测试结果"""
        summary = {}

        stats = results.get("stats", {})
        if stats:
            # 提取关键指标
            summary = {
                "total_requests": stats.get("Request Count", 0),
                "failure_count": stats.get("Failure Count", 0),
                "avg_response_time_ms": stats.get("Average Response Time", 0),
                "min_response_time_ms": stats.get("Min Response Time", 0),
                "max_response_time_ms": stats.get("Max Response Time", 0),
                "requests_per_second": stats.get("Requests/s", 0),
                "failure_rate": (
                    stats.get("Failure Count", 0)
                    / max(stats.get("Request Count", 1), 1)
                )
                * 100,
            }

            # 性能评级
            rps = summary.get("requests_per_second", 0)
            avg_time = summary.get("avg_response_time_ms", 0)
            failure_rate = summary.get("failure_rate", 0)

            summary["performance_grade"] = self._grade_stress_performance(
                rps, avg_time, failure_rate
            )

        return summary

    def _grade_api_performance(
        self, qps: float, avg_time: float, error_rate: float
    ) -> str:
        """API性能评级"""
        if qps >= 100 and avg_time < 200 and error_rate < 1:
            return "A"
        elif qps >= 50 and avg_time < 500 and error_rate < 5:
            return "B"
        elif qps >= 20 and avg_time < 1000 and error_rate < 10:
            return "C"
        else:
            return "D"

    def _grade_stress_performance(
        self, rps: float, avg_time: float, failure_rate: float
    ) -> str:
        """压力测试性能评级"""
        if rps >= 500 and avg_time < 500 and failure_rate < 1:
            return "A"
        elif rps >= 200 and avg_time < 1000 and failure_rate < 5:
            return "B"
        elif rps >= 100 and avg_time < 2000 and failure_rate < 10:
            return "C"
        else:
            return "D"

    def _check_compliance(self, summary: Dict) -> Dict:
        """检查性能合规性"""
        compliance = {}

        # API接口在1000并发下响应时间<500ms
        api_summary = summary.get("api", {})
        compliance["api_response_time"] = all(
            result.get("avg_response_time_ms", 999) < 500
            for result in api_summary.values()
        )

        # 数据库支持10000+QPS查询
        db_summary = summary.get("database", {})
        postgres_qps = db_summary.get("postgresql", {}).get("qps", 0)
        compliance["database_qps"] = postgres_qps >= 10000

        # 整体合规性
        compliance["overall"] = all(
            [
                compliance.get("api_response_time", False),
                compliance.get("database_qps", False),
            ]
        )

        return compliance

    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []

        compliance = analysis.get("compliance", {})
        summary = analysis.get("summary", {})

        # API性能建议
        if not compliance.get("api_response_time"):
            recommendations.append("API响应时间需要优化，考虑添加缓存或优化数据库查询")

        # 数据库性能建议
        if not compliance.get("database_qps"):
            recommendations.append(
                "数据库QPS不达标，考虑优化索引、连接池配置或数据库硬件"
            )

        # 具体优化建议
        api_summary = summary.get("api", {})
        for endpoint, result in api_summary.items():
            grade = result.get("performance_grade", "D")
            if grade in ["C", "D"]:
                recommendations.append(
                    f"端点 {endpoint} 性能较差(等级{grade})，需要专项优化"
                )

        db_summary = summary.get("database", {})
        if db_summary.get("postgresql", {}).get("performance_grade") in ["C", "D"]:
            recommendations.append("PostgreSQL性能需要优化，检查连接池、索引和查询计划")

        if db_summary.get("redis", {}).get("performance_grade") in ["C", "D"]:
            recommendations.append("Redis性能需要优化，检查内存配置和网络延迟")

        if not recommendations:
            recommendations.append("系统性能表现良好，继续保持当前配置")

        return recommendations

    def generate_final_report(self, analysis: Dict):
        """生成最终报告"""
        print("\n" + "=" * 80)
        print("柴管家系统性能测试最终报告")
        print("=" * 80)

        # 基本信息
        test_duration = analysis.get("test_duration_seconds", 0)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(
            f"测试时长: {int(test_duration//3600)}小时{int((test_duration%3600)//60)}分{int(test_duration%60)}秒"
        )
        print()

        # 合规性检查结果
        compliance = analysis.get("compliance", {})
        print("🎯 验收标准检查:")
        print(
            f"  {'✅' if compliance.get('api_response_time') else '❌'} API接口在1000并发下响应时间<500ms"
        )
        print(
            f"  {'✅' if compliance.get('database_qps') else '❌'} 数据库支持10000+QPS查询"
        )
        print(f"  {'✅' if compliance.get('overall') else '❌'} 整体性能达标")
        print()

        # 性能摘要
        summary = analysis.get("summary", {})

        if "api" in summary:
            print("🌐 API性能摘要:")
            for endpoint, result in summary["api"].items():
                grade = result.get("performance_grade", "N/A")
                qps = result.get("qps", 0)
                avg_time = result.get("avg_response_time_ms", 0)
                print(f"  {endpoint}: {qps:.1f} QPS, {avg_time:.1f}ms, 等级{grade}")
            print()

        if "database" in summary:
            print("🗄️ 数据库性能摘要:")
            db_summary = summary["database"]

            if "postgresql" in db_summary:
                pg = db_summary["postgresql"]
                print(
                    f"  PostgreSQL: {pg.get('qps', 0):.1f} QPS, {pg.get('avg_response_time_ms', 0):.1f}ms, 等级{pg.get('performance_grade', 'N/A')}"
                )

            if "redis" in db_summary:
                redis = db_summary["redis"]
                print(
                    f"  Redis: {redis.get('qps', 0):.1f} QPS, {redis.get('avg_response_time_ms', 0):.1f}ms, 等级{redis.get('performance_grade', 'N/A')}"
                )
            print()

        if "stress" in summary:
            print("💪 压力测试摘要:")
            stress = summary["stress"]
            print(f"  总请求数: {stress.get('total_requests', 0)}")
            print(f"  失败率: {stress.get('failure_rate', 0):.2f}%")
            print(f"  平均响应时间: {stress.get('avg_response_time_ms', 0):.1f}ms")
            print(f"  每秒请求数: {stress.get('requests_per_second', 0):.1f}")
            print(f"  性能等级: {stress.get('performance_grade', 'N/A')}")
            print()

        # 优化建议
        recommendations = analysis.get("recommendations", [])
        print("💡 优化建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        print()

        # 最终结论
        overall_pass = compliance.get("overall", False)
        print("📋 最终结论:")
        if overall_pass:
            print("  ✅ 系统性能测试通过，满足所有验收标准")
            print("  ✅ 系统可以投入生产环境使用")
        else:
            print("  ❌ 系统性能测试未完全通过")
            print("  ⚠️  建议进行性能优化后重新测试")

        print("=" * 80)

        # 保存报告
        results_dir = self.config.get("results_dir", "./performance_results")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(results_dir, f"final_report_{timestamp}.json")

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "analysis": analysis,
                    "test_results": self.results,
                    "config": self.config,
                },
                f,
                indent=2,
                ensure_ascii=False,
            )

        print(f"📄 详细报告已保存到: {report_file}")

    async def run_comprehensive_test(self):
        """运行综合性能测试"""
        self.start_time = time.time()

        try:
            # 准备测试环境
            if not self.prepare_test_environment():
                print("❌ 测试环境准备失败")
                return False

            # 启动系统监控
            self.start_system_monitoring()

            # 运行各项测试
            print("开始执行性能测试套件...")

            # 1. API基准测试
            self.results["api_benchmark"] = await self.run_api_benchmark_tests()

            # 2. 数据库性能测试
            self.results["database_performance"] = (
                await self.run_database_performance_tests()
            )

            # 3. Locust压力测试
            if self.config.get("run_locust", True):
                self.results["locust_stress"] = self.run_locust_stress_test()

            self.end_time = time.time()

            # 停止系统监控
            self.stop_system_monitoring()

            # 分析结果
            analysis = self.analyze_performance_results()

            # 生成最终报告
            self.generate_final_report(analysis)

            return analysis.get("compliance", {}).get("overall", False)

        except Exception as e:
            print(f"❌ 测试执行过程中发生错误: {e}")
            return False

        finally:
            if self.system_monitor:
                self.stop_system_monitoring()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="柴管家系统性能测试")
    parser.add_argument(
        "--api-url", default="http://localhost:8000", help="API服务器地址"
    )
    parser.add_argument(
        "--postgres-url",
        default="postgresql://admin:password@localhost:5432/chaiguanjia",
        help="PostgreSQL连接URL",
    )
    parser.add_argument(
        "--redis-url", default="redis://localhost:6379/0", help="Redis连接URL"
    )
    parser.add_argument(
        "--results-dir", default="./performance_results", help="测试结果输出目录"
    )
    parser.add_argument("--skip-locust", action="store_true", help="跳过Locust压力测试")
    parser.add_argument("--locust-users", type=int, default=1000, help="Locust用户数")
    parser.add_argument(
        "--locust-spawn-rate", type=int, default=50, help="Locust用户生成速率"
    )
    parser.add_argument(
        "--locust-duration", type=int, default=300, help="Locust测试时长(秒)"
    )

    args = parser.parse_args()

    # 配置测试参数
    config = {
        "api_url": args.api_url,
        "postgres_url": args.postgres_url,
        "redis_url": args.redis_url,
        "results_dir": args.results_dir,
        "run_locust": not args.skip_locust,
        "locust_users": args.locust_users,
        "locust_spawn_rate": args.locust_spawn_rate,
        "locust_duration": args.locust_duration,
    }

    # 创建并运行测试
    runner = PerformanceTestRunner(config)

    try:
        success = asyncio.run(runner.run_comprehensive_test())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
