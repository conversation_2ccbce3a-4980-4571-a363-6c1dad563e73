# database/connection.py - 数据库连接管理
import logging
from contextlib import asynccontextmanager, contextmanager
from typing import As<PERSON><PERSON><PERSON><PERSON>, Generator, Optional

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.exc import DisconnectionError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import NullPool, QueuePool

from ..models import Base
from .config import get_database_config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库连接管理器"""

    def __init__(self):
        self.config = get_database_config()
        self._engine: Optional[Engine] = None
        self._async_engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._async_session_factory: Optional[async_sessionmaker] = None

    def _create_engine(self) -> Engine:
        """创建同步数据库引擎"""
        if self._engine is None:
            # 构建连接参数
            connect_args = {}
            if self.config.DB_SSL_MODE != "disable":
                connect_args["sslmode"] = self.config.DB_SSL_MODE
                if self.config.DB_SSL_CERT:
                    connect_args["sslcert"] = self.config.DB_SSL_CERT
                if self.config.DB_SSL_KEY:
                    connect_args["sslkey"] = self.config.DB_SSL_KEY
                if self.config.DB_SSL_ROOT_CERT:
                    connect_args["sslrootcert"] = self.config.DB_SSL_ROOT_CERT

            self._engine = create_engine(
                self.config.database_url,
                poolclass=QueuePool,
                pool_size=self.config.DB_POOL_SIZE,
                max_overflow=self.config.DB_MAX_OVERFLOW,
                pool_timeout=self.config.DB_POOL_TIMEOUT,
                pool_recycle=self.config.DB_POOL_RECYCLE,
                pool_pre_ping=self.config.DB_POOL_PRE_PING,
                echo=self.config.DB_ECHO,
                echo_pool=self.config.DB_ECHO_POOL,
                connect_args=connect_args,
                future=True,
            )

            # 添加连接事件监听器
            self._setup_engine_events(self._engine)

        return self._engine

    def _create_async_engine(self):
        """创建异步数据库引擎"""
        if self._async_engine is None:
            self._async_engine = create_async_engine(
                self.config.async_database_url,
                pool_size=self.config.DB_POOL_SIZE,
                max_overflow=self.config.DB_MAX_OVERFLOW,
                pool_timeout=self.config.DB_POOL_TIMEOUT,
                pool_recycle=self.config.DB_POOL_RECYCLE,
                pool_pre_ping=self.config.DB_POOL_PRE_PING,
                echo=self.config.DB_ECHO,
                future=True,
            )
        return self._async_engine

    def _setup_engine_events(self, engine: Engine):
        """设置数据库引擎事件监听器"""

        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """连接时设置数据库参数"""
            if "postgresql" in str(dbapi_connection):
                # 设置PostgreSQL参数
                with dbapi_connection.cursor() as cursor:
                    # 设置默认时区
                    cursor.execute("SET timezone = 'Asia/Shanghai'")
                    # 设置默认编码
                    cursor.execute("SET client_encoding = 'UTF8'")

        @event.listens_for(engine, "checkout")
        def ping_connection(dbapi_connection, connection_record, connection_proxy):
            """检出连接时ping测试"""
            connection_record.info["pid"] = os.getpid()

        @event.listens_for(engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """归还连接时的处理"""
            logger.debug("Connection checked in to pool")

    @property
    def engine(self) -> Engine:
        """获取同步数据库引擎"""
        return self._create_engine()

    @property
    def async_engine(self):
        """获取异步数据库引擎"""
        return self._create_async_engine()

    @property
    def session_factory(self) -> sessionmaker:
        """获取同步会话工厂"""
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=self.engine,
                class_=Session,
                autoflush=False,
                autocommit=False,
                expire_on_commit=False,
            )
        return self._session_factory

    @property
    def async_session_factory(self) -> async_sessionmaker:
        """获取异步会话工厂"""
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                autoflush=False,
                autocommit=False,
                expire_on_commit=False,
            )
        return self._async_session_factory

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """获取同步数据库会话上下文管理器"""
        session = self.session_factory()
        try:
            logger.debug("Database session created")
            yield session
            session.commit()
            logger.debug("Database session committed")
        except Exception as e:
            logger.error(f"Database session error: {e}")
            session.rollback()
            raise
        finally:
            session.close()
            logger.debug("Database session closed")

    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取异步数据库会话上下文管理器"""
        session = self.async_session_factory()
        try:
            logger.debug("Async database session created")
            yield session
            await session.commit()
            logger.debug("Async database session committed")
        except Exception as e:
            logger.error(f"Async database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()
            logger.debug("Async database session closed")

    def create_all_tables(self):
        """创建所有数据表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("All database tables created successfully")
        except SQLAlchemyError as e:
            logger.error(f"Failed to create database tables: {e}")
            raise

    def drop_all_tables(self):
        """删除所有数据表"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("All database tables dropped successfully")
        except SQLAlchemyError as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
            logger.info("Database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

    async def test_async_connection(self) -> bool:
        """测试异步数据库连接"""
        try:
            async with self.get_async_session() as session:
                await session.execute("SELECT 1")
            logger.info("Async database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Async database connection test failed: {e}")
            return False

    def close_all_connections(self):
        """关闭所有数据库连接"""
        if self._engine:
            self._engine.dispose()
            logger.info("Sync database engine disposed")

        if self._async_engine:
            self._async_engine.dispose()
            logger.info("Async database engine disposed")


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数
def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话的便捷函数"""
    return db_manager.get_session()


async def get_async_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话的便捷函数"""
    async with db_manager.get_async_session() as session:
        yield session


def init_database():
    """初始化数据库"""
    logger.info("Initializing database...")

    # 测试连接
    if not db_manager.test_connection():
        raise Exception("Failed to connect to database")

    # 创建表
    db_manager.create_all_tables()

    logger.info("Database initialized successfully")


# 导入时自动初始化（可选）
import os

if os.getenv("AUTO_INIT_DB", "false").lower() == "true":
    init_database()
