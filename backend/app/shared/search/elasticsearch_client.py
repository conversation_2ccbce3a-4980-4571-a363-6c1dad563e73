"""
Elasticsearch 客户端模块

提供异步 Elasticsearch 客户端连接和基础操作。
"""

import logging
from typing import Any, Dict, List, Optional

try:
    from elasticsearch import AsyncElasticsearch
    from elasticsearch.exceptions import NotFoundError
except ImportError:
    raise ImportError("请安装 elasticsearch 包: pip install elasticsearch")

from ...core.config import settings

logger = logging.getLogger(__name__)


class ElasticsearchClient:
    """Elasticsearch 异步客户端"""

    def __init__(self, hosts: Optional[List[str]] = None):
        """
        初始化 Elasticsearch 客户端

        Args:
            hosts: Elasticsearch 主机列表
        """
        self.hosts = hosts or [
            f"http://{settings.ELASTICSEARCH_HOST}:" f"{settings.ELASTICSEARCH_PORT}"
        ]
        self.client: Optional[AsyncElasticsearch] = None
        self._connected = False

    async def connect(self) -> bool:
        """
        连接到 Elasticsearch

        Returns:
            bool: 连接是否成功
        """
        try:
            self.client = AsyncElasticsearch(
                hosts=self.hosts,
                timeout=30,
                retry_on_timeout=True,
                max_retries=3,
                # 开发环境禁用 SSL 验证
                verify_certs=False,
                ssl_show_warn=False,
            )

            # 测试连接
            health = await self.client.cluster.health()
            if health:
                self._connected = True
                logger.info(f"Elasticsearch 连接成功: {self.hosts}")
                logger.info(f"集群状态: {health.get('status', 'unknown')}")
                return True

        except Exception as e:
            logger.error(f"Elasticsearch 连接失败: {e}")
            self._connected = False

        return False

    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.close()
            self._connected = False
            logger.info("Elasticsearch 连接已关闭")

    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

    async def ping(self) -> bool:
        """
        检查连接状态

        Returns:
            bool: 连接是否正常
        """
        if not self.client:
            return False

        try:
            return await self.client.ping()
        except Exception as e:
            logger.warning(f"Elasticsearch ping 失败: {e}")
            return False

    async def get_cluster_info(self) -> Dict[str, Any]:
        """
        获取集群信息

        Returns:
            Dict: 集群信息
        """
        if not self.client:
            return {}

        try:
            info = await self.client.info()
            health = await self.client.cluster.health()
            stats = await self.client.cluster.stats()

            return {
                "cluster_name": info.get("cluster_name"),
                "version": info.get("version", {}).get("number"),
                "status": health.get("status"),
                "number_of_nodes": health.get("number_of_nodes"),
                "number_of_data_nodes": health.get("number_of_data_nodes"),
                "active_primary_shards": health.get("active_primary_shards"),
                "active_shards": health.get("active_shards"),
                "indices_count": stats.get("indices", {}).get("count"),
                "docs_count": stats.get("indices", {}).get("docs", {}).get("count"),
                "store_size": stats.get("indices", {})
                .get("store", {})
                .get("size_in_bytes"),
            }

        except Exception as e:
            logger.error(f"获取集群信息失败: {e}")
            return {}

    # 索引操作
    async def create_index(
        self,
        index: str,
        mapping: Optional[Dict[str, Any]] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        创建索引

        Args:
            index: 索引名称
            mapping: 字段映射
            settings: 索引设置

        Returns:
            bool: 创建是否成功
        """
        if not self.client:
            return False

        try:
            body = {}
            if mapping:
                body["mappings"] = mapping
            if settings:
                body["settings"] = settings

            await self.client.indices.create(index=index, body=body)
            logger.info(f"索引创建成功: {index}")
            return True

        except Exception as e:
            logger.error(f"创建索引失败 {index}: {e}")
            return False

    async def delete_index(self, index: str) -> bool:
        """
        删除索引

        Args:
            index: 索引名称

        Returns:
            bool: 删除是否成功
        """
        if not self.client:
            return False

        try:
            await self.client.indices.delete(index=index)
            logger.info(f"索引删除成功: {index}")
            return True

        except NotFoundError:
            logger.warning(f"索引不存在: {index}")
            return True
        except Exception as e:
            logger.error(f"删除索引失败 {index}: {e}")
            return False

    async def index_exists(self, index: str) -> bool:
        """
        检查索引是否存在

        Args:
            index: 索引名称

        Returns:
            bool: 索引是否存在
        """
        if not self.client:
            return False

        try:
            return await self.client.indices.exists(index=index)
        except Exception as e:
            logger.error(f"检查索引存在性失败 {index}: {e}")
            return False

    async def refresh_index(self, index: str) -> bool:
        """
        刷新索引

        Args:
            index: 索引名称

        Returns:
            bool: 刷新是否成功
        """
        if not self.client:
            return False

        try:
            await self.client.indices.refresh(index=index)
            return True
        except Exception as e:
            logger.error(f"刷新索引失败 {index}: {e}")
            return False

    # 文档操作
    async def index_document(
        self, index: str, document: Dict[str, Any], doc_id: Optional[str] = None
    ) -> Optional[str]:
        """
        索引文档

        Args:
            index: 索引名称
            document: 文档内容
            doc_id: 文档ID（可选）

        Returns:
            Optional[str]: 文档ID
        """
        if not self.client:
            return None

        try:
            result = await self.client.index(index=index, body=document, id=doc_id)
            return result.get("_id")

        except Exception as e:
            logger.error(f"索引文档失败 {index}: {e}")
            return None

    async def get_document(self, index: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文档

        Args:
            index: 索引名称
            doc_id: 文档ID

        Returns:
            Optional[Dict]: 文档内容
        """
        if not self.client:
            return None

        try:
            result = await self.client.get(index=index, id=doc_id)
            return result.get("_source")

        except NotFoundError:
            return None
        except Exception as e:
            logger.error(f"获取文档失败 {index}/{doc_id}: {e}")
            return None

    async def update_document(
        self, index: str, doc_id: str, document: Dict[str, Any]
    ) -> bool:
        """
        更新文档

        Args:
            index: 索引名称
            doc_id: 文档ID
            document: 更新内容

        Returns:
            bool: 更新是否成功
        """
        if not self.client:
            return False

        try:
            await self.client.update(index=index, id=doc_id, body={"doc": document})
            return True

        except Exception as e:
            logger.error(f"更新文档失败 {index}/{doc_id}: {e}")
            return False

    async def delete_document(self, index: str, doc_id: str) -> bool:
        """
        删除文档

        Args:
            index: 索引名称
            doc_id: 文档ID

        Returns:
            bool: 删除是否成功
        """
        if not self.client:
            return False

        try:
            await self.client.delete(index=index, id=doc_id)
            return True

        except NotFoundError:
            return True
        except Exception as e:
            logger.error(f"删除文档失败 {index}/{doc_id}: {e}")
            return False

    async def bulk_index(
        self,
        index: str,
        documents: List[Dict[str, Any]],
        doc_ids: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        批量索引文档

        Args:
            index: 索引名称
            documents: 文档列表
            doc_ids: 文档ID列表（可选）

        Returns:
            Dict: 批量操作结果
        """
        if not self.client:
            return {"errors": True, "items": []}

        try:
            actions = []
            for i, doc in enumerate(documents):
                action = {"_index": index, "_source": doc}
                if doc_ids and i < len(doc_ids):
                    action["_id"] = doc_ids[i]

                actions.append({"index": action})

            result = await self.client.bulk(body=actions)
            return result

        except Exception as e:
            logger.error(f"批量索引失败 {index}: {e}")
            return {"errors": True, "items": []}

    # 搜索操作
    async def search(
        self,
        index: str,
        query: Dict[str, Any],
        size: int = 10,
        from_: int = 0,
        sort: Optional[List[Dict[str, Any]]] = None,
        highlight: Optional[Dict[str, Any]] = None,
        source: Optional[Union[bool, List[str]]] = None,
    ) -> Dict[str, Any]:
        """
        搜索文档

        Args:
            index: 索引名称
            query: 搜索查询
            size: 返回结果数量
            from_: 起始位置
            sort: 排序规则
            highlight: 高亮配置
            source: 返回字段

        Returns:
            Dict: 搜索结果
        """
        if not self.client:
            return {"hits": {"total": {"value": 0}, "hits": []}}

        try:
            body = {"query": query}

            if sort:
                body["sort"] = sort
            if highlight:
                body["highlight"] = highlight
            if source is not None:
                body["_source"] = source

            result = await self.client.search(
                index=index, body=body, size=size, from_=from_
            )
            return result

        except Exception as e:
            logger.error(f"搜索失败 {index}: {e}")
            return {"hits": {"total": {"value": 0}, "hits": []}}


# 全局客户端实例
es_client = ElasticsearchClient()


async def init_elasticsearch() -> bool:
    """
    初始化 Elasticsearch 连接

    Returns:
        bool: 初始化是否成功
    """
    return await es_client.connect()


async def close_elasticsearch():
    """关闭 Elasticsearch 连接"""
    await es_client.close()


def get_es_client() -> ElasticsearchClient:
    """
    获取 Elasticsearch 客户端实例

    Returns:
        ElasticsearchClient: 客户端实例
    """
    return es_client
